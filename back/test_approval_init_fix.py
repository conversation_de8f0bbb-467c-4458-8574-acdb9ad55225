#!/usr/bin/env python3
"""
测试审批记录初始化修复
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.get_db import get_db


async def test_approval_init_fix():
    """测试审批记录初始化修复"""
    
    # 获取数据库会话
    async for db in get_db():
        try:
            print("=" * 60)
            print("测试审批记录初始化修复")
            print("=" * 60)
            
            # 1. 清理现有的审批记录（测试用）
            print("\n1. 清理现有的审批记录（测试用）:")
            
            # 删除所有审批记录
            await db.execute(text("DELETE FROM project_quotation_approval_record"))
            await db.commit()
            print("✅ 已清理所有审批记录")
            
            # 2. 测试新建项目不会自动创建审批记录
            print("\n2. 验证新建项目不会自动创建审批记录:")
            
            # 查询所有项目的审批记录数量
            projects_result = await db.execute(text("""
                SELECT pq.id, pq.project_name, pq.business_type, pq.status,
                       COUNT(pqar.id) as record_count
                FROM project_quotation pq
                LEFT JOIN project_quotation_approval_record pqar ON pq.id = pqar.project_quotation_id
                WHERE pq.status = '0'
                GROUP BY pq.id, pq.project_name, pq.business_type, pq.status
                ORDER BY pq.create_time DESC
                LIMIT 5
            """))
            projects = projects_result.fetchall()
            
            print("草稿状态项目的审批记录:")
            all_zero = True
            for project in projects:
                project_id, project_name, business_type, status, record_count = project
                print(f"  - {project_name} (ID: {project_id}, {business_type}): {record_count} 条审批记录")
                if record_count > 0:
                    all_zero = False
            
            if all_zero:
                print("✅ 草稿状态项目没有审批记录（符合预期）")
            else:
                print("❌ 草稿状态项目有审批记录（不符合预期）")
            
            # 3. 测试提交审核时初始化审批记录
            print("\n3. 测试提交审核时初始化审批记录:")
            
            # 导入审批服务
            from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
            from module_system.entity.vo.current_user_vo import CurrentUserModel
            from module_system.entity.model.sys_user_model import SysUser
            
            # 创建测试用户
            test_user = CurrentUserModel(
                user=SysUser(user_id=1, user_name="admin"),
                roles=["admin"]
            )
            
            approval_service = ProjectQuotationApprovalService(db)
            
            # 测试一般采样项目
            if projects:
                sampling_project = None
                sample_project = None
                
                for project in projects:
                    if project[2] == "sampling" and not sampling_project:
                        sampling_project = project
                    elif project[2] == "sample" and not sample_project:
                        sample_project = project
                
                # 测试一般采样项目
                if sampling_project:
                    project_id = sampling_project[0]
                    project_name = sampling_project[1]
                    
                    print(f"\n测试一般采样项目: {project_name} (ID: {project_id})")
                    
                    # 初始化审批记录（不传递business_type，让它从数据库查询）
                    await approval_service.init_approval_records(project_id, current_user=test_user)
                    
                    # 验证审批记录
                    records_result = await db.execute(text("""
                        SELECT approver_type, approval_stage, is_required
                        FROM project_quotation_approval_record 
                        WHERE project_quotation_id = :project_id
                        ORDER BY approval_stage, approver_type
                    """), {"project_id": project_id})
                    records = records_result.fetchall()
                    
                    print(f"  生成的审批记录 ({len(records)} 条):")
                    expected_types = {"market", "lab", "field"}
                    actual_types = {record[0] for record in records}
                    
                    for record in records:
                        required_text = "必需" if record[2] == "1" else "可选"
                        print(f"    - 阶段{record[1]}: {record[0]} 审批 ({required_text})")
                    
                    if len(records) == 3 and actual_types == expected_types:
                        print("  ✅ 一般采样项目审批记录正确")
                    else:
                        print(f"  ❌ 一般采样项目审批记录错误，期望3条(market,lab,field)，实际{len(records)}条({actual_types})")
                
                # 测试送样项目
                if sample_project:
                    project_id = sample_project[0]
                    project_name = sample_project[1]
                    
                    print(f"\n测试送样项目: {project_name} (ID: {project_id})")
                    
                    # 初始化审批记录（不传递business_type，让它从数据库查询）
                    await approval_service.init_approval_records(project_id, current_user=test_user)
                    
                    # 验证审批记录
                    records_result = await db.execute(text("""
                        SELECT approver_type, approval_stage, is_required
                        FROM project_quotation_approval_record 
                        WHERE project_quotation_id = :project_id
                        ORDER BY approval_stage, approver_type
                    """), {"project_id": project_id})
                    records = records_result.fetchall()
                    
                    print(f"  生成的审批记录 ({len(records)} 条):")
                    expected_types = {"market", "lab"}
                    actual_types = {record[0] for record in records}
                    
                    for record in records:
                        required_text = "必需" if record[2] == "1" else "可选"
                        print(f"    - 阶段{record[1]}: {record[0]} 审批 ({required_text})")
                    
                    if len(records) == 2 and actual_types == expected_types:
                        print("  ✅ 送样项目审批记录正确")
                    else:
                        print(f"  ❌ 送样项目审批记录错误，期望2条(market,lab)，实际{len(records)}条({actual_types})")
            
            # 4. 验证最终结果
            print("\n4. 验证最终结果:")
            
            # 统计各业务类型的审批记录
            summary_result = await db.execute(text("""
                SELECT pq.business_type, COUNT(pqar.id) as total_records,
                       COUNT(DISTINCT pq.id) as project_count,
                       GROUP_CONCAT(DISTINCT pqar.approver_type ORDER BY pqar.approver_type) as approval_types
                FROM project_quotation pq
                LEFT JOIN project_quotation_approval_record pqar ON pq.id = pqar.project_quotation_id
                WHERE pqar.id IS NOT NULL
                GROUP BY pq.business_type
                ORDER BY pq.business_type
            """))
            summary = summary_result.fetchall()
            
            print("审批记录统计:")
            for row in summary:
                business_type, total_records, project_count, approval_types = row
                avg_records = total_records / project_count if project_count > 0 else 0
                print(f"  - {business_type}: {project_count} 个项目, {total_records} 条记录, 平均 {avg_records:.1f} 条/项目")
                print(f"    审批类型: {approval_types}")
            
            print("\n" + "=" * 60)
            print("✅ 审批记录初始化修复测试完成")
            print("=" * 60)
            
            print("\n修复总结:")
            print("🔧 主要改进:")
            print("   1. 新建项目时不再自动创建审批记录")
            print("   2. 提交审核时才初始化审批记录")
            print("   3. 初始化时从数据库查询项目的实际业务类型")
            print("   4. 严格按照业务类型创建对应的审批记录")
            print("\n📋 审批流程:")
            print("   - 一般采样项目 (sampling): 市场 + 实验室 + 现场审批")
            print("   - 送样项目 (sample): 市场 + 实验室审批")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()
        break


if __name__ == "__main__":
    asyncio.run(test_approval_init_fix())

#!/usr/bin/env python3
"""
测试新的审批流程
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.get_db import get_db


async def test_new_approval_flow():
    """测试新的审批流程"""
    
    # 获取数据库会话
    async for db in get_db():
        try:
            print("=" * 60)
            print("测试新的审批流程")
            print("=" * 60)
            
            # 1. 检查用户niangao的角色
            print("\n1. 检查用户niangao的角色:")
            user_role_result = await db.execute(text("""
                SELECT u.user_name, u.user_id, r.role_name, r.role_key 
                FROM sys_user u
                JOIN sys_user_role ur ON u.user_id = ur.user_id
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE u.user_name = 'niangao' AND r.status = '0'
            """))
            user_roles = user_role_result.fetchall()
            
            if user_roles:
                print(f"✅ 用户niangao的角色:")
                for role in user_roles:
                    print(f"   - {role[2]} ({role[3]})")
                    
                # 检查是否有市场审批人员角色
                has_market_role = any(role[3] == 'market-approver' for role in user_roles)
                if has_market_role:
                    print("✅ 用户具有市场审批人员角色")
                else:
                    print("❌ 用户没有市场审批人员角色")
            else:
                print("❌ 用户niangao没有任何角色")
                return
            
            # 2. 检查审批记录
            print("\n2. 检查审批记录:")
            approval_records_result = await db.execute(text("""
                SELECT pqar.*, pq.project_name, pq.business_type
                FROM project_quotation_approval_record pqar
                JOIN project_quotation pq ON pqar.project_quotation_id = pq.id
                WHERE pqar.approver_type = 'market' 
                AND pqar.approval_status = 'pending' 
                AND pqar.is_required = '1'
                ORDER BY pqar.create_time DESC
            """))
            market_records = approval_records_result.fetchall()
            
            print(f"✅ 找到 {len(market_records)} 条市场审批记录:")
            for record in market_records:
                print(f"   - 项目: {record[7]} (ID: {record[1]})")
                print(f"     审批类型: {record[2]}, 状态: {record[3]}")
                print(f"     业务类型: {record[8]}")
            
            # 3. 模拟审批列表查询
            print("\n3. 模拟审批列表查询:")
            pending_list_result = await db.execute(text("""
                SELECT pq.id, pq.project_name, pq.project_code, pq.business_type, 
                       pq.customer_name, pqar.approver_type, pqar.approval_stage,
                       pq.create_time, pq.status
                FROM project_quotation pq
                JOIN project_quotation_approval_record pqar ON pq.id = pqar.project_quotation_id
                WHERE pqar.approval_status = 'pending' 
                AND pqar.is_required = '1'
                AND pqar.approver_type = 'market'
                ORDER BY pq.create_time DESC
            """))
            pending_projects = pending_list_result.fetchall()
            
            print(f"✅ 用户niangao可以看到的待审批项目: {len(pending_projects)} 个")
            for project in pending_projects:
                print(f"   - {project[1]} ({project[2]})")
                print(f"     客户: {project[4]}, 业务类型: {project[3]}")
                print(f"     审批类型: {project[5]}, 阶段: {project[6]}")
            
            # 4. 检查所有审批人员配置
            print("\n4. 检查所有审批人员配置:")
            
            # 市场审批人员
            market_approvers_result = await db.execute(text("""
                SELECT u.user_name, u.nick_name
                FROM sys_user u
                JOIN sys_user_role ur ON u.user_id = ur.user_id
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE r.role_key = 'market-approver' 
                AND r.status = '0' AND u.status = '0' AND u.del_flag = '0'
            """))
            market_approvers = market_approvers_result.fetchall()
            print(f"✅ 市场审批人员: {[f'{a[1]}({a[0]})' for a in market_approvers]}")
            
            # 实验室审批人员
            lab_approvers_result = await db.execute(text("""
                SELECT u.user_name, u.nick_name
                FROM sys_user u
                JOIN sys_user_role ur ON u.user_id = ur.user_id
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE r.role_key = 'lab-approver' 
                AND r.status = '0' AND u.status = '0' AND u.del_flag = '0'
            """))
            lab_approvers = lab_approvers_result.fetchall()
            print(f"✅ 实验室审批人员: {[f'{a[1]}({a[0]})' for a in lab_approvers]}")
            
            # 现场审批人员
            field_approvers_result = await db.execute(text("""
                SELECT u.user_name, u.nick_name
                FROM sys_user u
                JOIN sys_user_role ur ON u.user_id = ur.user_id
                JOIN sys_role r ON ur.role_id = r.role_id
                WHERE r.role_key = 'field-approver' 
                AND r.status = '0' AND u.status = '0' AND u.del_flag = '0'
            """))
            field_approvers = field_approvers_result.fetchall()
            print(f"✅ 现场审批人员: {[f'{a[1]}({a[0]})' for a in field_approvers]}")
            
            print("\n" + "=" * 60)
            print("✅ 新的审批流程测试完成")
            print("=" * 60)
            print("\n总结:")
            print("- 审批记录现在只记录流程，不绑定具体用户")
            print("- 审批人根据用户角色动态确定")
            print("- 用户niangao作为市场审批人员可以审批所有待审核项目的市场审批环节")
            print("- 实际审批时会记录具体的审批人")
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            await db.close()
        break


if __name__ == "__main__":
    asyncio.run(test_new_approval_flow())

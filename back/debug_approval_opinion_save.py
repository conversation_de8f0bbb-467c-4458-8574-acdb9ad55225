#!/usr/bin/env python3
"""
调试技术手册表category_codes字段问题的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from urllib.parse import quote_plus
from config.database import async_engine, AsyncSessionLocal


async def check_table_structure():
    """
    检查technical_manual表结构
    """
    print("正在检查technical_manual表结构...")
    
    engine = async_engine
    
    try:
        async with engine.begin() as conn:
            # 检查表结构
            result = await conn.execute(text("DESCRIBE technical_manual"))
            columns = result.fetchall()
            
            print("\n当前表结构:")
            print("-" * 80)
            for column in columns:
                print(f"{column[0]:<25} {column[1]:<20} {column[2]:<10} {column[3]:<10} {column[4]:<15} {column[5] or ''}")
            
            # 检查是否存在category_codes字段
            column_names = [col[0] for col in columns]
            has_category_codes = 'category_codes' in column_names
            
            print(f"\ncategory_codes字段存在: {has_category_codes}")
            
            if not has_category_codes:
                print("\n需要执行迁移脚本添加category_codes字段")
                return False
            else:
                print("\ncategory_codes字段已存在")
                return True
                
    except Exception as e:
        print(f"检查表结构时出错: {e}")
        return False
    finally:
        await engine.dispose()


async def execute_migration():
    """
    执行迁移脚本添加category_codes字段
    """
    print("\n正在执行迁移脚本...")
    
    engine = async_engine
    
    try:
        async with engine.begin() as conn:
            # 检查是否存在category_code字段
            check_result = await conn.execute(text("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'lims' 
                AND TABLE_NAME = 'technical_manual' 
                AND COLUMN_NAME = 'category_code'
            """))
            has_old_field = check_result.fetchone() is not None
            
            if has_old_field:
                # 1. 备份现有数据
                print("1. 备份现有数据...")
                await conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS technical_manual_category_code_backup AS 
                    SELECT id, category_code 
                    FROM technical_manual 
                    WHERE category_code IS NOT NULL AND category_code != ''
                """))
            
            # 2. 添加新的JSON字段
            print("2. 添加category_codes字段...")
            await conn.execute(text("""
                ALTER TABLE technical_manual 
                ADD COLUMN category_codes JSON COMMENT '类目编码列表（JSON数组格式）' AFTER test_code
            """))
            
            if has_old_field:
                # 3. 迁移现有类目编码数据到JSON格式
                print("3. 迁移现有数据...")
                await conn.execute(text("""
                    UPDATE technical_manual 
                    SET category_codes = JSON_ARRAY(category_code) 
                    WHERE category_code IS NOT NULL AND category_code != ''
                """))
                
                # 4. 验证数据迁移
                print("4. 验证数据迁移...")
                result = await conn.execute(text("""
                    SELECT 
                        id,
                        category_code as old_category_code,
                        category_codes as new_category_codes,
                        JSON_LENGTH(category_codes) as category_count
                    FROM technical_manual 
                    WHERE category_codes IS NOT NULL
                    LIMIT 5
                """))
                
                rows = result.fetchall()
                if rows:
                    print("\n迁移验证结果:")
                    print("-" * 80)
                    for row in rows:
                        print(f"ID: {row[0]}, 旧字段: {row[1]}, 新字段: {row[2]}, 数组长度: {row[3]}")
            else:
                print("3. 没有旧的category_code字段，跳过数据迁移")
            
            print("\n迁移完成!")
            
    except Exception as e:
        print(f"执行迁移时出错: {e}")
        raise
    finally:
        await engine.dispose()


async def test_json_contains():
    """
    测试JSON_CONTAINS查询
    """
    print("\n正在测试JSON_CONTAINS查询...")
    
    engine = async_engine
    
    try:
        async with engine.begin() as conn:
            # 测试查询
            result = await conn.execute(text("""
                SELECT id, category_codes 
                FROM technical_manual 
                WHERE JSON_CONTAINS(category_codes, '"CATE00001"')
                LIMIT 5
            """))
            
            rows = result.fetchall()
            print(f"\n找到 {len(rows)} 条包含CATE00001的记录:")
            for row in rows:
                print(f"ID: {row[0]}, category_codes: {row[1]}")
                
    except Exception as e:
        print(f"测试JSON_CONTAINS查询时出错: {e}")
    finally:
        await engine.dispose()


async def main():
    """
    主函数
    """
    print("开始调试technical_manual表category_codes字段问题...")
    
    # 检查表结构
    has_field = await check_table_structure()
    
    if not has_field:
        # 执行迁移
        try:
            await execute_migration()
            print("\n迁移成功完成!")
        except Exception as e:
            print(f"\n迁移失败: {e}")
            return
    
    # 测试JSON_CONTAINS查询
    await test_json_contains()
    
    print("\n调试完成!")


if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
"""
数据库迁移脚本：修改审批记录表结构
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.get_db import get_db


async def migrate_approval_table():
    """修改审批记录表结构"""
    
    # 获取数据库会话
    async for db in get_db():
        try:
            print("=" * 60)
            print("修改审批记录表结构")
            print("=" * 60)
            
            # 1. 修改 approver_user_id 字段允许为空
            print("\n1. 修改 approver_user_id 字段允许为空:")
            await db.execute(text("""
                ALTER TABLE project_quotation_approval_record 
                MODIFY COLUMN approver_user_id BIGINT NULL COMMENT '实际审批人用户ID（审批时填入）'
            """))
            print("✅ approver_user_id 字段已修改为允许为空")
            
            # 2. 提交更改
            await db.commit()
            print("✅ 数据库表结构修改完成")
            
        except Exception as e:
            print(f"❌ 迁移过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            await db.rollback()
        finally:
            await db.close()
        break


if __name__ == "__main__":
    asyncio.run(migrate_approval_table())

# 审批阶段控制修复总结

## 问题描述

用户反映一般采样项目在市场审批后直接显示为"审批完成"，没有继续进行实验室和现场审批阶段。

## 问题分析

通过代码审查和调试，发现了以下关键问题：

### 1. 重复的方法定义导致逻辑失效

**文件**: `back/module_quotation/service/project_quotation_approval_service.py`

**问题**: 存在两个 `_can_approve_at_stage` 方法定义：

```python
# 第一个定义（第417-427行）- 有问题的版本
async def _can_approve_at_stage(self, project_quotation_id: int, stage: int) -> bool:
    if stage == 1:
        # 第一阶段（市场审批）总是可以进行
        return True
    # 没有处理第二阶段的逻辑

# 第二个定义（第495-521行）- 正确的版本
async def _can_approve_at_stage(self, project_quotation_id: int, stage: int) -> bool:
    if stage == 1:
        return True  # 第一阶段总是可以审批

    if stage == 2:
        # 第二阶段需要第一阶段的所有必需审批都通过
        # ... 正确的逻辑
```

**影响**: Python会使用第一个定义，导致第二阶段的审批控制逻辑完全失效。

### 2. 审批流程设计

根据需求文档，一般采样项目的审批流程应该是：
1. **第一阶段**: 市场审批（必需）
2. **第二阶段**: 实验室审批 + 现场审批（都是必需，可并行）

但由于阶段控制逻辑失效，admin用户可以跳过阶段限制，导致审批流程异常。

## 修复方案

### 1. 删除重复的方法定义

**修复内容**:
- 删除第417-427行的错误方法定义
- 保留第495-521行的正确实现

**修复后的逻辑**:
```python
async def _can_approve_at_stage(self, project_quotation_id: int, stage: int) -> bool:
    if stage == 1:
        return True  # 第一阶段总是可以审批

    if stage == 2:
        # 第二阶段需要第一阶段的所有必需审批都通过
        stage1_stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                ProjectQuotationApprovalRecord.approval_stage == 1,
                ProjectQuotationApprovalRecord.is_required == "1"
            )
        )
        stage1_result = await self.db.execute(stage1_stmt)
        stage1_records = stage1_result.scalars().all()

        # 检查第一阶段是否都已通过
        return all(record.approval_status == "approved" for record in stage1_records)

    return False
```

### 2. 审批流程验证

修复后的审批流程：

1. **初始化阶段**: 
   - 创建市场审批记录（阶段1，必需）
   - 创建实验室审批记录（阶段2，必需）
   - 创建现场审批记录（阶段2，必需，仅一般采样）

2. **第一阶段审批**:
   - 只能进行市场审批
   - 市场审批通过后，项目状态应显示为"待审核（实验室|现场）"

3. **第二阶段审批**:
   - 只有在第一阶段完成后才能进行
   - 实验室和现场审批可以并行进行
   - 两个都通过后，项目状态变为"审核完成"

## 修复效果

### ✅ 阶段控制恢复正常
- 第一阶段审批后不会直接完成
- 必须等待第二阶段的所有必需审批都通过

### ✅ 审批流程符合需求
- 一般采样：市场 → 实验室 + 现场
- 送样：市场 → 实验室

### ✅ 状态显示准确
- "待审核（市场）"：第一阶段待审批
- "待审核（实验室|现场）"：第二阶段待审批
- "审核完成"：所有阶段都已通过

## 测试验证

通过测试脚本验证了：

1. **阶段控制逻辑**:
   - 第1阶段：总是返回 `True`
   - 第2阶段：只有在第1阶段完成后才返回 `True`
   - 第3阶段：返回 `False`

2. **方法调用**:
   - 确认只有一个 `_can_approve_at_stage` 方法定义
   - 方法逻辑正确执行

## 根本原因

这个问题的根本原因是：
1. **代码重复**: 同一个方法被定义了两次
2. **测试不足**: 没有充分测试审批阶段控制逻辑
3. **代码审查**: 重复定义没有被及时发现

## 预防措施

1. **代码审查**: 加强对重复定义的检查
2. **单元测试**: 为审批流程编写完整的测试用例
3. **集成测试**: 测试完整的审批流程场景
4. **静态分析**: 使用工具检测重复定义

现在审批阶段控制已经修复，一般采样项目将按照正确的流程进行：市场审批 → 实验室审批 + 现场审批 → 审核完成。

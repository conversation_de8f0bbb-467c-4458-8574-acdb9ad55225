# 报价审批弹窗状态显示修复总结

## 修复概述

本次修复解决了报价审批弹窗页面中的状态显示问题，确保与列表页的状态显示逻辑保持一致。

## 修复的问题

### 1. 弹窗当前状态显示为空
**问题**：弹窗中的"当前状态"字段显示为空
**原因**：弹窗使用 `quotation.approvalStatus` 字段，但后端没有返回该字段
**修复**：实现了与列表页一致的状态计算逻辑

### 2. 审批历史记录状态显示为英文
**问题**：历史记录中的状态显示为 "pending"、"approved"、"rejected" 等英文
**原因**：使用了旧的状态映射方法
**修复**：更新为中文状态标签："待审批"、"已通过"、"已拒绝"

### 3. 列表页表头文字
**问题**：表头显示"当前审批状态"
**修复**：改为"当前状态"，与项目报价页面保持一致

## 修复内容

### 1. 列表页修复 (`front/src/views/quotation/project-quotation-approval/index.vue`)
- 修改表头文字：`"当前审批状态"` → `"当前状态"`

### 2. 弹窗组件修复 (`front/src/views/quotation/project-quotation-approval/components/ApprovalDialog.vue`)

#### 数据字段新增
```javascript
data() {
  return {
    // ... 其他字段
    currentDetailedStatus: null,        // 当前详细状态
    currentDetailedStatusLabel: null,   // 当前状态标签
  }
}
```

#### 状态计算逻辑
- 新增 `calculateCurrentStatus()` 方法，根据项目状态和审批记录计算详细状态
- 支持以下状态显示：
  - `草稿` (status: '0')
  - `待审核（市场）` (status: '1-market')
  - `待审核（实验室）` (status: '1-lab')
  - `待审核（现场）` (status: '1-field')
  - `待审核（实验室|现场）` (status: '1-lab|field')
  - `审核完成` (status: '2')
  - `已撤回` (status: '3')
  - `已拒绝` (status: '4')

#### 状态映射方法更新
- `getStatusLabel()` - 与列表页一致的状态标签
- `getStatusTagType()` - 与列表页一致的标签颜色
- `getRecordStatusLabel()` - 审批记录状态标签（中文）
- `getRecordStatusTagType()` - 审批记录状态颜色
- `getApproverTypeLabel()` - 审批人类型标签

#### 模板更新
- 当前状态显示：使用计算出的详细状态和标签
- 历史记录状态：使用中文状态标签
- 审批人类型：使用正确的字段名 `approverType`
- 审批意见：使用正确的字段名 `approvalOpinion`

## 修复效果

### ✅ 当前状态正确显示
- 弹窗中的"当前状态"字段现在能正确显示详细状态
- 状态标签颜色与列表页保持一致
- 支持所有审批阶段的状态显示

### ✅ 审批历史记录中文化
- 状态显示：`"待审批"`、`"已通过"`、`"已拒绝"`
- 审批人类型：`"市场审批"`、`"实验室审批"`、`"现场审批"`
- 审批意见正确显示

### ✅ 界面一致性
- 列表页和弹窗页面的状态显示逻辑完全一致
- 状态标签颜色统一
- 表头文字统一

## 测试验证

通过测试脚本验证了：
1. 后端API正确返回审批状态数据
2. 前端状态计算逻辑正确
3. 状态映射和显示正确

## 技术细节

### 状态计算逻辑
```javascript
// 根据项目状态和审批记录计算详细状态
if (status === '1') { // 待审核状态
  const pendingRecords = records.filter(r => r.approvalStatus === 'pending')
  // 根据待审批记录的类型确定具体阶段
  if (marketPending) return '1-market'
  if (labPending && fieldPending) return '1-lab|field'
  // ... 其他逻辑
}
```

### 状态标签映射
```javascript
const statusMap = {
  '0': '草稿',
  '1': '待审核', 
  '2': '审核完成',
  '3': '已撤回',
  '4': '已拒绝'
}
```

现在报价审批弹窗的状态显示已经完全修复，与列表页保持一致。

"""
项目报价审批服务
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, select, func, or_, delete, update
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.dao.project_quotation_approval_record_dao import ProjectQuotationApprovalRecordDao
from module_quotation.entity.vo.project_quotation_approval_record_vo import (
    ApprovalActionModel,
    ApprovalStatusModel,
    ProjectQuotationApprovalStatusModel,
    AddProjectQuotationApprovalRecordModel
)
from utils.common_util import CamelCaseUtil


class ProjectQuotationApprovalService(BaseService[ProjectQuotationApprovalRecord]):
    """
    项目报价审批服务
    """

    def __init__(self, db: AsyncSession):
        """
        初始化

        :param db: 数据库会话
        """
        super().__init__(ProjectQuotationApprovalRecord, db)
        self.db = db

    async def init_approval_records(self, project_quotation_id: int, business_type: str = None, current_user: CurrentUserModel = None):
        """
        初始化审批记录

        :param project_quotation_id: 项目报价ID
        :param business_type: 业务类别（可选，如果不提供则从数据库查询）
        :param current_user: 当前用户（可选）
        """
        # 导入必要的模块（select已经在文件顶部导入了）

        # 如果没有提供业务类型，从数据库查询
        if not business_type:
            quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
            quotation_result = await self.db.execute(quotation_stmt)
            quotation = quotation_result.scalar_one_or_none()

            if not quotation:
                raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

            business_type = quotation.business_type
            if not business_type:
                raise ServiceException(message="项目业务类型不能为空")

        # 验证业务类型
        if business_type not in ["sampling", "sample"]:
            raise ServiceException(message=f"无效的业务类型: {business_type}")

        # 设置默认的创建用户ID
        create_by = current_user.user.user_id if current_user else 1

        # 获取当前最大审批轮次
        max_round_stmt = select(func.max(ProjectQuotationApprovalRecord.approval_round)).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        max_round_result = await self.db.execute(max_round_stmt)
        max_round = max_round_result.scalar() or 0
        new_round = max_round + 1
        
        # 将已存在的当前轮次审批记录标记为非当前轮次（只保留已操作过的记录）
        # 只保留已通过或已拒绝的记录，删除待审批的记录
        update_stmt = (
            update(ProjectQuotationApprovalRecord)
            .where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.is_current_round == "1",
                    or_(
                        ProjectQuotationApprovalRecord.approval_status == "approved",
                        ProjectQuotationApprovalRecord.approval_status == "rejected"
                    )
                )
            )
            .values(is_current_round="0")
        )
        await self.db.execute(update_stmt)
        
        # 删除待审批状态的记录
        delete_pending_stmt = (
            delete(ProjectQuotationApprovalRecord)
            .where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.is_current_round == "1",
                    ProjectQuotationApprovalRecord.approval_status == "pending"
                )
            )
        )
        await self.db.execute(delete_pending_stmt)

        approval_records = []

        # 市场审批（第一阶段，必需）
        market_record = ProjectQuotationApprovalRecord(
            project_quotation_id=project_quotation_id,
            approver_type="market",
            approval_stage=1,
            is_required="1",
            approval_round=new_round,
            is_current_round="1",
            create_by=create_by,
            create_time=datetime.now()
        )
        approval_records.append(market_record)

        # 项目成单确认（第二阶段，必需）- 由提单人或项目客服确认
        order_confirm_record = ProjectQuotationApprovalRecord(
            project_quotation_id=project_quotation_id,
            approver_type="order_confirm",
            approval_stage=2,
            is_required="1",
            approval_round=new_round,
            is_current_round="1",
            create_by=create_by,
            create_time=datetime.now()
        )
        approval_records.append(order_confirm_record)

        # 实验室审批（第三阶段，必需）
        lab_record = ProjectQuotationApprovalRecord(
            project_quotation_id=project_quotation_id,
            approver_type="lab",
            approval_stage=3,
            is_required="1",
            approval_round=new_round,
            is_current_round="1",
            create_by=create_by,
            create_time=datetime.now()
        )
        approval_records.append(lab_record)

        # 现场审批（第三阶段，仅一般采样项目需要）
        if business_type == "sampling":
            field_record = ProjectQuotationApprovalRecord(
                project_quotation_id=project_quotation_id,
                approver_type="field",
                approval_stage=3,
                is_required="1",
                approval_round=new_round,
                is_current_round="1",
                create_by=create_by,
                create_time=datetime.now()
            )
            approval_records.append(field_record)

        # 批量保存
        for record in approval_records:
            self.db.add(record)
        await self.db.commit()

    async def get_approval_records_with_history(self, project_quotation_id: int) -> List[Dict[str, Any]]:
        """
        获取项目报价的所有审批记录（包括历史记录）

        :param project_quotation_id: 项目报价ID
        :return: 包含历史记录的审批记录列表
        """
        dao = ProjectQuotationApprovalRecordDao(self.db)
        records = await dao.get_all_approval_records_with_history(project_quotation_id)
        
        result = []
        for record in records:
            record_dict = {
                "id": record.id,
                "project_quotation_id": record.project_quotation_id,
                "approver_type": record.approver_type,
                "approval_status": record.approval_status,
                "approval_opinion": record.approval_opinion,
                "approval_time": record.approval_time.isoformat() if record.approval_time else None,
                "approver_user_id": record.approver_user_id,
                "approval_stage": record.approval_stage,
                "is_required": record.is_required,
                "approval_round": record.approval_round,
                "is_current_round": record.is_current_round,
                "create_time": record.create_time.isoformat() if record.create_time else None,
                "approver_name": record.approver_user.nick_name if record.approver_user else None
            }
            result.append(record_dict)
        
        return result

    async def _get_users_by_role(self, role_key: str) -> List[SysUser]:
        """
        根据角色编码获取用户列表

        :param role_key: 角色编码
        :return: 用户列表
        """
        stmt = (
            select(SysUser)
            .join(SysUserRole, SysUser.user_id == SysUserRole.user_id)
            .join(SysRole, SysUserRole.role_id == SysRole.role_id)
            .where(
                and_(
                    SysRole.role_key == role_key,
                    SysRole.status == "0",  # 角色正常
                    SysUser.status == "0",  # 用户正常
                    SysUser.del_flag == "0"  # 用户未删除
                )
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def _get_order_confirm_approvers(self, project_quotation_id: int) -> List[SysUser]:
        """
        获取项目成单确认审批人（提单人和项目客服）

        :param project_quotation_id: 项目报价ID
        :return: 审批人列表
        """
        approvers = []
        
        # 获取项目报价信息
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()
        
        if quotation:
            # 1. 添加提单人（创建人）
            creator_stmt = select(SysUser).where(
                and_(
                    SysUser.user_name == quotation.create_by,
                    SysUser.status == "0",
                    SysUser.del_flag == "0"
                )
            )
            creator_result = await self.db.execute(creator_stmt)
            creator = creator_result.scalar_one_or_none()
            if creator:
                approvers.append(creator)
            
            # 2. 添加项目客服
            # 通过项目报价客服关联表获取客服用户
            from module_quotation.entity.do.project_quotation_customer_support_do import ProjectQuotationCustomerSupport
            customer_support_stmt = (
                select(SysUser)
                .join(
                    ProjectQuotationCustomerSupport,
                    SysUser.user_id == ProjectQuotationCustomerSupport.user_id
                )
                .where(
                    and_(
                        ProjectQuotationCustomerSupport.project_quotation_id == project_quotation_id,
                        SysUser.status == "0",
                        SysUser.del_flag == "0"
                    )
                )
            )
            customer_support_result = await self.db.execute(customer_support_stmt)
            customer_supports = customer_support_result.scalars().all()
            approvers.extend(customer_supports)
        
        return approvers

    async def get_approval_status(self, project_quotation_id: int) -> ProjectQuotationApprovalStatusModel:
        """
        获取项目报价审批状态

        :param project_quotation_id: 项目报价ID
        :return: 审批状态
        """
        # 获取项目报价信息
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

        # 获取审批记录（使用DAO方法确保正确排序）
        dao = ProjectQuotationApprovalRecordDao(self.db)
        records = await dao.get_all_approval_records_with_history(project_quotation_id)

        approval_records = []
        for record in records:
            # 获取该审批类型的所有可能审批人
            if record.approver_type == "order_confirm":
                # 项目成单确认：获取提单人和项目客服
                approvers = await self._get_order_confirm_approvers(project_quotation_id)
            else:
                # 其他审批类型：按角色获取审批人
                approvers = await self._get_users_by_role(f"{record.approver_type}-approver")
            
            approver_names = [user.nick_name for user in approvers]

            # 如果已经有实际审批人，显示实际审批人姓名
            actual_approver_name = None
            if record.approver_user_id:
                approver_stmt = select(SysUser).where(SysUser.user_id == record.approver_user_id)
                approver_result = await self.db.execute(approver_stmt)
                approver_user = approver_result.scalar_one_or_none()
                if approver_user:
                    actual_approver_name = approver_user.nick_name

            approval_records.append(ApprovalStatusModel(
                approverType=record.approver_type,
                approvalStatus=record.approval_status,
                approverName=actual_approver_name or ', '.join(approver_names),
                approvalTime=record.approval_time,
                approvalOpinion=record.approval_opinion,
                approvalStage=record.approval_stage,
                isRequired=record.is_required
            ))

        # 计算整体状态
        overall_status = self._calculate_overall_status(records, quotation.business_type)

        return ProjectQuotationApprovalStatusModel(
            projectQuotationId=project_quotation_id,
            businessType=quotation.business_type,
            overallStatus=overall_status,
            approvalRecords=approval_records
        )

    def _calculate_overall_status(self, records: List, business_type: str) -> str:
        """
        计算整体审批状态

        :param records: 审批记录列表
        :param business_type: 业务类别
        :return: 整体状态
        """
        if not records:
            return "0"  # 草稿

        # 检查是否有拒绝的审批
        if any(record.approval_status == "rejected" for record in records):
            return "4"  # 已拒绝

        # 获取必需的审批记录
        required_records = [record for record in records if record.is_required == "1"]

        if not required_records:
            return "0"  # 草稿

        # 检查是否所有必需审批都已通过
        all_approved = all(record.approval_status == "approved" for record in required_records)

        if all_approved:
            return "2"  # 审核完成

        # 检查是否有审批在进行中
        has_pending = any(record.approval_status == "pending" for record in required_records)

        if has_pending:
            return "1"  # 待审核

        return "0"  # 草稿

    def _calculate_detailed_status(self, records: List, business_type: str) -> str:
        """
        计算详细的审批状态，包含当前审批阶段信息

        :param records: 审批记录列表
        :param business_type: 业务类别
        :return: 详细状态，如 "1-market", "1-lab", "1-field", "1-lab|field"
        """
        if not records:
            return "0"  # 草稿

        # 检查是否有拒绝的审批
        if any(record.approval_status == "rejected" for record in records):
            return "4"  # 已拒绝

        # 获取必需的审批记录
        required_records = [record for record in records if record.is_required == "1"]

        if not required_records:
            return "0"  # 草稿

        # 检查是否所有必需审批都已通过
        all_approved = all(record.approval_status == "approved" for record in required_records)

        if all_approved:
            return "2"  # 审核完成

        # 分析当前待审批的阶段
        pending_records = [record for record in required_records if record.approval_status == "pending"]

        if not pending_records:
            return "0"  # 草稿

        # 按阶段分组
        stage1_pending = [r for r in pending_records if r.approval_stage == 1]
        stage2_pending = [r for r in pending_records if r.approval_stage == 2]
        stage3_pending = [r for r in pending_records if r.approval_stage == 3]

        # 如果第一阶段有待审批，返回市场审批
        if stage1_pending:
            return "1-market"

        # 如果第二阶段有待审批，返回项目成单确认
        if stage2_pending:
            pending_types = [r.approver_type for r in stage2_pending]

            # 检查是否可以进行第二阶段审批（第一阶段必须完成）
            stage1_records = [r for r in required_records if r.approval_stage == 1]
            stage1_completed = all(r.approval_status == "approved" for r in stage1_records)

            if not stage1_completed:
                return "1-market"  # 第一阶段未完成，仍显示市场审批

            # 第二阶段审批分析
            if "order_confirm" in pending_types:
                return "1-order_confirm"  # 项目成单确认

        # 如果第三阶段有待审批，分析具体类型
        if stage3_pending:
            pending_types = [r.approver_type for r in stage3_pending]

            # 检查是否可以进行第三阶段审批（第二阶段必须完成）
            stage2_records = [r for r in required_records if r.approval_stage == 2]
            stage2_completed = all(r.approval_status == "approved" for r in stage2_records)

            if not stage2_completed:
                # 检查第一阶段是否完成，决定显示哪个阶段
                stage1_records = [r for r in required_records if r.approval_stage == 1]
                stage1_completed = all(r.approval_status == "approved" for r in stage1_records)
                if not stage1_completed:
                    return "1-market"  # 第一阶段未完成
                else:
                    return "1-order_confirm"  # 第二阶段未完成

            # 第三阶段审批分析
            if "lab" in pending_types and "field" in pending_types:
                return "1-lab|field"  # 实验室和现场都需要审批
            elif "lab" in pending_types:
                return "1-lab"  # 只需要实验室审批
            elif "field" in pending_types:
                return "1-field"  # 只需要现场审批

        return "1"  # 默认待审核

    async def submit_for_approval(self, project_quotation_id: int, current_user: CurrentUserModel):
        """
        提交审批

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        """
        # 更新项目报价状态为待审核
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

        quotation.status = "1"  # 待审核
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

        await self.db.commit()

    async def perform_approval(self, project_quotation_id: int, approval_action: ApprovalActionModel, current_user: CurrentUserModel):
        """
        执行审批操作

        :param project_quotation_id: 项目报价ID
        :param approval_action: 审批操作
        :param current_user: 当前用户
        """
        # 检查当前用户是否有审批权限
        is_admin = 'admin' in current_user.roles

        # 检查用户是否有审批权限
        user_approval_record = await self._get_user_approvable_record(project_quotation_id, current_user)

        if not user_approval_record:
            raise ServiceException(message="您没有该项目的审批权限或没有待审批的记录")

        if user_approval_record.approval_status != "pending":
            raise ServiceException(message="该审批已处理，无法重复操作")

        # 检查审批阶段是否可以进行
        if not await self._can_approve_at_stage(project_quotation_id, user_approval_record.approval_stage):
            raise ServiceException(message="当前阶段审批条件不满足，无法进行审批")

        # 更新审批记录
        user_approval_record.approval_status = approval_action.approval_status
        user_approval_record.approval_opinion = approval_action.approval_opinion
        user_approval_record.approval_time = datetime.now()
        user_approval_record.approver_user_id = current_user.user.user_id  # 记录实际审批人
        user_approval_record.update_by = current_user.user.user_id
        user_approval_record.update_time = datetime.now()

        # 更新项目报价整体状态
        await self._update_quotation_status(project_quotation_id, current_user)

        await self.db.commit()

    async def _get_user_approvable_record(self, project_quotation_id: int, current_user: CurrentUserModel) -> Optional[ProjectQuotationApprovalRecord]:
        """
        获取用户可以审批的记录

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        :return: 可审批的记录
        """
        is_admin = 'admin' in current_user.roles

        if is_admin:
            # admin用户可以审批任何待审批的记录，选择第一个
            stmt = select(ProjectQuotationApprovalRecord).where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.approval_status == "pending",
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            ).order_by(
                ProjectQuotationApprovalRecord.approval_stage,
                ProjectQuotationApprovalRecord.approver_type
            )
            result = await self.db.execute(stmt)
            return result.scalars().first()
        else:
            # 普通用户需要检查角色权限
            user_roles = current_user.roles

            # 获取所有待审批记录
            stmt = select(ProjectQuotationApprovalRecord).where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.approval_status == "pending",
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            ).order_by(
                ProjectQuotationApprovalRecord.approval_stage,
                ProjectQuotationApprovalRecord.approver_type
            )
            result = await self.db.execute(stmt)
            pending_records = result.scalars().all()

            # 检查用户是否有对应的审批权限
            for record in pending_records:
                if record.approver_type == "order_confirm":
                    # 项目成单确认阶段：检查是否为提单人或项目客服
                    if await self._can_user_approve_order_confirm(project_quotation_id, current_user.user.user_id):
                        return record
                else:
                    # 其他阶段：按角色检查
                    required_role = f"{record.approver_type}-approver"
                    if required_role in user_roles:
                        return record

            return None

    async def _can_user_approve_order_confirm(self, project_quotation_id: int, user_id: int) -> bool:
        """
        检查用户是否可以审批项目成单确认阶段
        
        :param project_quotation_id: 项目报价ID
        :param user_id: 用户ID
        :return: 是否可以审批
        """
        # 获取项目报价信息
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()
        
        if not quotation:
            return False
            
        # 检查是否为提单人（创建人）
        if quotation.create_by == user_id:
            return True
            
        # 检查是否为项目客服
        if quotation.customer_service_user_id == user_id:
            return True
            
        return False

    async def _get_project_detailed_status(self, project_quotation_id: int) -> str:
        """
        获取项目的详细状态

        :param project_quotation_id: 项目报价ID
        :return: 详细状态
        """
        # 查询项目报价
        stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        result = await self.db.execute(stmt)
        quotation = result.scalar_one_or_none()

        if not quotation:
            return "0"  # 草稿

        # 如果不是待审核状态，直接返回原状态
        if quotation.status != "1":
            return quotation.status

        # 获取审批记录
        records_stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        records_result = await self.db.execute(records_stmt)
        records = records_result.scalars().all()

        # 计算详细状态
        detailed_status = self._calculate_detailed_status(records, quotation.business_type)

        # 如果项目状态是"1"但详细状态计算结果是"0"，说明审批记录有问题
        # 这种情况下应该返回"1"而不是"0"，避免前端按钮显示错误
        if detailed_status == "0" and quotation.status == "1":
            return "1"

        return detailed_status

    def _get_detailed_status_label(self, detailed_status: str) -> str:
        """
        获取详细状态标签

        :param detailed_status: 详细状态值
        :return: 详细状态标签
        """
        if detailed_status == "0":
            return "草稿"
        elif detailed_status == "2":
            return "审核完成"
        elif detailed_status == "3":
            return "已撤回"
        elif detailed_status == "4":
            return "已拒绝"
        elif detailed_status == "1-market":
            return "待审核（市场）"
        elif detailed_status == "1-lab":
            return "待审核（实验室）"
        elif detailed_status == "1-field":
            return "待审核（现场）"
        elif detailed_status == "1-lab|field":
            return "待审核（实验室|现场）"
        elif detailed_status == "1":
            return "待审核"
        else:
            return "未知"

    async def _can_approve_at_stage(self, project_quotation_id: int, stage: int) -> bool:
        """
        检查是否可以在指定阶段进行审批

        :param project_quotation_id: 项目报价ID
        :param stage: 审批阶段
        :return: 是否可以审批
        """
        if stage == 1:
            return True  # 第一阶段总是可以审批

        if stage == 2:
            # 第二阶段需要第一阶段的所有必需审批都通过
            stage1_stmt = select(ProjectQuotationApprovalRecord).where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.approval_stage == 1,
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            )
            stage1_result = await self.db.execute(stage1_stmt)
            stage1_records = stage1_result.scalars().all()

            # 检查第一阶段是否都已通过
            return all(record.approval_status == "approved" for record in stage1_records)

        if stage == 3:
            # 第三阶段需要第二阶段的所有必需审批都通过
            stage2_stmt = select(ProjectQuotationApprovalRecord).where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.approval_stage == 2,
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            )
            stage2_result = await self.db.execute(stage2_stmt)
            stage2_records = stage2_result.scalars().all()

            # 检查第二阶段是否都已通过
            return all(record.approval_status == "approved" for record in stage2_records)

        if stage == 4:
            # 第四阶段需要第三阶段的所有必需审批都通过
            stage3_stmt = select(ProjectQuotationApprovalRecord).where(
                and_(
                    ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                    ProjectQuotationApprovalRecord.approval_stage == 3,
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            )
            stage3_result = await self.db.execute(stage3_stmt)
            stage3_records = stage3_result.scalars().all()

            # 检查第三阶段是否都已通过
            return all(record.approval_status == "approved" for record in stage3_records)

        return False

    async def _update_quotation_status(self, project_quotation_id: int, current_user: CurrentUserModel):
        """
        更新项目报价整体状态

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        """
        # 获取项目报价
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        # 获取所有审批记录
        records_stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        records_result = await self.db.execute(records_stmt)
        records = records_result.scalars().all()

        # 计算新状态
        new_status = self._calculate_overall_status(records, quotation.business_type)

        # 更新状态
        quotation.status = new_status
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

    async def get_pending_approvals(self, current_user: CurrentUserModel) -> List[Dict[str, Any]]:
        """
        获取当前用户待审批的项目列表

        :param current_user: 当前用户
        :return: 待审批项目列表
        """
        is_admin = 'admin' in current_user.roles
        user_roles = current_user.roles

        # 获取所有待审批的项目
        stmt = (
            select(ProjectQuotation, ProjectQuotationApprovalRecord)
            .join(
                ProjectQuotationApprovalRecord,
                ProjectQuotation.id == ProjectQuotationApprovalRecord.project_quotation_id
            )
            .where(
                and_(
                    ProjectQuotationApprovalRecord.approval_status == "pending",
                    ProjectQuotationApprovalRecord.is_required == "1"
                )
            )
            .order_by(ProjectQuotation.create_time.desc())
        )

        result = await self.db.execute(stmt)
        data = result.all()

        pending_list = []
        processed_quotations = set()  # 用于去重，避免同一个项目出现多次

        for quotation, approval_record in data:
            # 避免重复添加同一个项目
            if quotation.id in processed_quotations:
                continue

            # 检查用户是否有审批权限
            can_approve = False
            if is_admin:
                can_approve = True
            else:
                # 检查用户是否有对应的审批角色
                required_role = f"{approval_record.approver_type}-approver"
                can_approve = required_role in user_roles

            # 检查审批阶段是否可以进行
            if can_approve:
                can_approve = await self._can_approve_at_stage(quotation.id, approval_record.approval_stage)

            if can_approve:
                # 计算详细状态
                detailed_status = await self._get_project_detailed_status(quotation.id)
                detailed_status_label = self._get_detailed_status_label(detailed_status)

                pending_list.append(CamelCaseUtil.transform_result({
                    "id": quotation.id,
                    "project_name": quotation.project_name,
                    "project_code": quotation.project_code,
                    "business_type": quotation.business_type,
                    "customer_name": quotation.customer_name,
                    "approver_type": approval_record.approver_type,
                    "approval_stage": approval_record.approval_stage,
                    "create_time": quotation.create_time,
                    "status": quotation.status,
                    "detailed_status": detailed_status,
                    "detailed_status_label": detailed_status_label
                }))
                processed_quotations.add(quotation.id)

        return pending_list

    async def get_pending_approvals_paginated(self, current_user: CurrentUserModel, query_params: dict) -> dict:
        """
        获取当前用户待审批的项目列表（分页）

        :param current_user: 当前用户
        :param query_params: 查询参数
        :return: 分页结果
        """
        page_num = query_params.get('page_num', 1)
        page_size = query_params.get('page_size', 10)
        project_name = query_params.get('project_name')
        project_code = query_params.get('project_code')
        customer_name = query_params.get('customer_name')
        business_type = query_params.get('business_type')

        is_admin = 'admin' in current_user.roles

        # 构建基础查询条件
        base_conditions = [
            ProjectQuotationApprovalRecord.approval_status == "pending",
            ProjectQuotationApprovalRecord.is_required == "1"
        ]

        # 添加搜索条件
        if project_name:
            base_conditions.append(ProjectQuotation.project_name.like(f"%{project_name}%"))
        if project_code:
            base_conditions.append(ProjectQuotation.project_code.like(f"%{project_code}%"))
        if customer_name:
            base_conditions.append(ProjectQuotation.customer_name.like(f"%{customer_name}%"))
        if business_type:
            base_conditions.append(ProjectQuotation.business_type == business_type)

        # 所有用户都可以看到待审批项目，但后续会根据权限过滤
        where_conditions = and_(*base_conditions)

        # 查询总数
        count_stmt = (
            select(func.count(func.distinct(ProjectQuotation.id)))
            .select_from(
                ProjectQuotation.__table__.join(
                    ProjectQuotationApprovalRecord.__table__,
                    ProjectQuotation.id == ProjectQuotationApprovalRecord.project_quotation_id
                )
            )
            .where(where_conditions)
        )

        total_result = await self.db.execute(count_stmt)
        total = total_result.scalar() or 0

        # 分页查询数据
        offset = (page_num - 1) * page_size

        data_stmt = (
            select(ProjectQuotation, ProjectQuotationApprovalRecord)
            .select_from(
                ProjectQuotation.__table__.join(
                    ProjectQuotationApprovalRecord.__table__,
                    ProjectQuotation.id == ProjectQuotationApprovalRecord.project_quotation_id
                )
            )
            .where(where_conditions)
            .order_by(ProjectQuotation.create_time.desc())
            .offset(offset)
            .limit(page_size)
        )

        result = await self.db.execute(data_stmt)
        data = result.all()

        pending_list = []
        processed_quotations = set()  # 用于去重，避免同一个项目出现多次

        for quotation, approval_record in data:
            # 避免重复添加同一个项目
            if quotation.id in processed_quotations:
                continue

            # 检查用户是否有审批权限
            can_approve = False
            if is_admin:
                can_approve = True
            else:
                # 检查用户是否有对应的审批角色
                required_role = f"{approval_record.approver_type}-approver"
                can_approve = required_role in current_user.roles

            # 检查审批阶段是否可以进行
            if can_approve:
                can_approve = await self._can_approve_at_stage(quotation.id, approval_record.approval_stage)

            if can_approve:
                # 计算详细状态
                detailed_status = await self._get_project_detailed_status(quotation.id)
                detailed_status_label = self._get_detailed_status_label(detailed_status)

                pending_list.append(CamelCaseUtil.transform_result({
                    "id": quotation.id,
                    "project_name": quotation.project_name,
                    "project_code": quotation.project_code,
                    "business_type": quotation.business_type,
                    "customer_name": quotation.customer_name,
                    "approver_type": approval_record.approver_type,
                    "approval_stage": approval_record.approval_stage,
                    "create_time": quotation.create_time,
                    "status": quotation.status,
                    "detailed_status": detailed_status,
                    "detailed_status_label": detailed_status_label
                }))
                processed_quotations.add(quotation.id)

        return {
            "list": pending_list,
            "total": total,
            "page_num": page_num,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size if total > 0 else 0
        }

    async def withdraw_approval(self, project_quotation_id: int, current_user: CurrentUserModel) -> bool:
        """
        撤回审批

        :param project_quotation_id: 项目报价ID
        :param current_user: 当前用户
        :return: 是否成功
        """
        # 查询项目报价
        quotation_stmt = select(ProjectQuotation).where(ProjectQuotation.id == project_quotation_id)
        quotation_result = await self.db.execute(quotation_stmt)
        quotation = quotation_result.scalar_one_or_none()

        if not quotation:
            raise ServiceException(message=f"项目报价不存在：{project_quotation_id}")

        # 检查项目状态是否为待审核
        if quotation.status != "1":
            raise ServiceException(message="只有待审核状态的项目才能撤回")

        # 检查权限：admin用户有所有权限，其他用户只能撤回自己创建的项目
        is_creator = str(quotation.create_by) == str(current_user.user.user_id)
        is_admin = 'admin' in current_user.roles

        if not (is_creator or is_admin):
            raise ServiceException(message="只有项目创建人或管理员才能撤回审批")

        # 删除所有审批记录
        delete_records_stmt = delete(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        await self.db.execute(delete_records_stmt)

        # 更新项目状态为草稿
        quotation.status = "0"  # 草稿
        quotation.update_by = current_user.user.user_id
        quotation.update_time = datetime.now()

        await self.db.commit()

        return True

#!/usr/bin/env python3
"""
调试审批记录排序问题
"""

import sys
import os
import asyncio
from sqlalchemy import select

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.database import get_async_session
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord


async def debug_approval_order():
    """
    调试审批记录排序
    """
    async with get_async_session() as session:
        # 查询所有审批记录
        stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == 1
        ).order_by(
            ProjectQuotationApprovalRecord.is_current_round.asc(),
            ProjectQuotationApprovalRecord.approval_round.asc(),
            ProjectQuotationApprovalRecord.approval_stage,
            ProjectQuotationApprovalRecord.approver_type
        )
        
        result = await session.execute(stmt)
        records = result.scalars().all()
        
        print("=== 审批记录排序调试 ===")
        print(f"总记录数: {len(records)}")
        print()
        
        for i, record in enumerate(records, 1):
            print(f"记录 {i}:")
            print(f"  ID: {record.id}")
            print(f"  审批轮次: {record.approval_round}")
            print(f"  是否当前轮次: {record.is_current_round}")
            print(f"  审批阶段: {record.approval_stage}")
            print(f"  审批人类型: {record.approver_type}")
            print(f"  审批状态: {record.approval_status}")
            print(f"  创建时间: {record.create_time}")
            print(f"  更新时间: {record.update_time}")
            print()


if __name__ == "__main__":
    asyncio.run(debug_approval_order())
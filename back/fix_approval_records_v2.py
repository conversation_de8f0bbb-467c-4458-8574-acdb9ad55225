#!/usr/bin/env python3
"""
修复脚本v2：重新初始化审批记录（新的流程设计）
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from config.get_db import get_db


async def fix_approval_records_v2():
    """重新初始化审批记录（新的流程设计）"""

    # 获取数据库会话
    async for db in get_db():
        try:
            print("=" * 60)
            print("重新初始化审批记录（新的流程设计）")
            print("=" * 60)

            # 1. 获取所有待审核项目
            print("\n1. 获取待审核项目:")
            project_result = await db.execute(text("SELECT id, project_name, business_type FROM project_quotation WHERE status = '1'"))
            projects = project_result.fetchall()

            if not projects:
                print("❌ 没有待审核的项目!")
                return

            print(f"✅ 找到 {len(projects)} 个待审核项目")

            # 2. 为每个项目重新生成审批记录
            print("\n2. 重新生成审批记录:")

            for project in projects:
                project_id = project[0]
                project_name = project[1]
                business_type = project[2]

                print(f"\n处理项目: {project_name} (ID: {project_id})")

                # 删除已存在的审批记录
                await db.execute(text("DELETE FROM project_quotation_approval_record WHERE project_quotation_id = :project_id"),
                               {"project_id": project_id})
                print(f"  删除已存在的审批记录")

                # 市场审批（第一阶段，必需）
                await db.execute(text("""
                    INSERT INTO project_quotation_approval_record
                    (project_quotation_id, approver_type, approver_user_id, approval_stage, is_required, approval_status, create_by, create_time)
                    VALUES (:project_id, 'market', NULL, 1, '1', 'pending', 1, NOW())
                """), {"project_id": project_id})
                print(f"  创建市场审批记录（流程）")

                # 实验室审批（第二阶段，必需）
                await db.execute(text("""
                    INSERT INTO project_quotation_approval_record
                    (project_quotation_id, approver_type, approver_user_id, approval_stage, is_required, approval_status, create_by, create_time)
                    VALUES (:project_id, 'lab', NULL, 2, '1', 'pending', 1, NOW())
                """), {"project_id": project_id})
                print(f"  创建实验室审批记录（流程）")

                # 现场审批（第二阶段，根据业务类型确定是否必需）
                is_field_required = "1" if business_type == "sampling" else "0"
                await db.execute(text("""
                    INSERT INTO project_quotation_approval_record
                    (project_quotation_id, approver_type, approver_user_id, approval_stage, is_required, approval_status, create_by, create_time)
                    VALUES (:project_id, 'field', NULL, 2, :is_required, 'pending', 1, NOW())
                """), {"project_id": project_id, "is_required": is_field_required})
                required_text = "必需" if is_field_required == "1" else "可选"
                print(f"  创建现场审批记录（流程，{required_text}）")

                print(f"  ✅ 为项目 {project_name} 创建了 3 条审批流程记录")

            # 提交事务
            await db.commit()

            print("\n" + "=" * 60)
            print("✅ 审批记录重新初始化完成!")
            print("=" * 60)

            # 3. 验证修复结果
            print("\n3. 验证修复结果:")

            # 统计各类型的审批记录
            market_result = await db.execute(text("SELECT COUNT(*) FROM project_quotation_approval_record WHERE approver_type = 'market'"))
            market_count = market_result.scalar()

            lab_result = await db.execute(text("SELECT COUNT(*) FROM project_quotation_approval_record WHERE approver_type = 'lab'"))
            lab_count = lab_result.scalar()

            field_result = await db.execute(text("SELECT COUNT(*) FROM project_quotation_approval_record WHERE approver_type = 'field'"))
            field_count = field_result.scalar()

            print(f"✅ 市场审批流程记录: {market_count} 条")
            print(f"✅ 实验室审批流程记录: {lab_count} 条")
            print(f"✅ 现场审批流程记录: {field_count} 条")

            print("\n说明:")
            print("- 审批记录现在只记录流程，不绑定具体用户")
            print("- 审批人根据用户角色动态确定")
            print("- 实际审批时会记录具体的审批人")

        except Exception as e:
            print(f"❌ 修复过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            await db.rollback()
        finally:
            await db.close()
        break


if __name__ == "__main__":
    asyncio.run(fix_approval_records_v2())

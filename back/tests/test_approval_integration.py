"""
项目报价审批流程集成测试
"""

import pytest
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.do.user_do import SysUser, SysUserRole
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService


class TestApprovalIntegration:
    """
    审批流程集成测试类
    """

    @pytest.fixture
    async def setup_integration_test_data(self, db: AsyncSession):
        """
        设置集成测试数据
        """
        # 创建测试角色
        market_role = SysRole(
            role_id=1,
            role_name="市场审批人员",
            role_key="market-approver",
            role_sort=10,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_role = SysRole(
            role_id=2,
            role_name="实验室审批人员", 
            role_key="lab-approver",
            role_sort=11,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_role = SysRole(
            role_id=3,
            role_name="现场审批人员",
            role_key="field-approver", 
            role_sort=12,
            status="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([market_role, lab_role, field_role])
        await db.flush()

        # 创建测试用户
        market_user = SysUser(
            user_id=1,
            user_name="market_user",
            nick_name="市场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        lab_user = SysUser(
            user_id=2,
            user_name="lab_user",
            nick_name="实验室用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        field_user = SysUser(
            user_id=3,
            user_name="field_user",
            nick_name="现场用户",
            password="$2b$12$test_password_hash",
            status="0",
            del_flag="0",
            create_by="admin",
            create_time=datetime.now()
        )
        
        db.add_all([market_user, lab_user, field_user])
        await db.flush()

        # 创建用户角色关联
        user_roles = [
            SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
            SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
            SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
        ]
        db.add_all(user_roles)

        # 创建测试项目报价
        quotation_sampling = ProjectQuotation(
            id=1,
            project_name="一般采样测试项目",
            project_code="SAMPLING001",
            business_type="sampling",
            status="0",
            customer_name="测试客户",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        
        quotation_sample = ProjectQuotation(
            id=2,
            project_name="送样测试项目",
            project_code="SAMPLE001",
            business_type="sample",
            status="0",
            customer_name="测试客户",
            create_by=market_user.user_id,
            create_time=datetime.now()
        )
        
        db.add_all([quotation_sampling, quotation_sample])
        await db.flush()

        await db.commit()

        return {
            "quotation_sampling": quotation_sampling,
            "quotation_sample": quotation_sample,
            "market_user": market_user,
            "lab_user": lab_user,
            "field_user": field_user,
            "market_role": market_role,
            "lab_role": lab_role,
            "field_role": field_role
        }

    @pytest.mark.asyncio
    async def test_complete_sampling_approval_workflow(self, setup_integration_test_data):
        """
        测试完整的一般采样审批流程
        """
        test_data = await setup_integration_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        lab_user = test_data["lab_user"]
        field_user = test_data["field_user"]
        db = quotation.__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 1. 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)

        # 验证初始化结果
        status = await approval_service.get_approval_status(quotation.id)
        assert status.overall_status == "0"  # 草稿
        assert len(status.approval_records) == 3  # 三类审批人

        # 2. 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)
        
        status = await approval_service.get_approval_status(quotation.id)
        assert status.overall_status == "1"  # 待审核

        # 3. 市场审批通过
        market_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="市场审批通过"
        )
        await approval_service.perform_approval(quotation.id, market_action, market_current_user)

        # 验证市场审批后状态
        status = await approval_service.get_approval_status(quotation.id)
        market_record = next(r for r in status.approval_records if r.approver_type == "market")
        assert market_record.approval_status == "approved"

        # 4. 实验室审批通过
        lab_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=lab_user.user_id, user_name=lab_user.user_name)
        )
        lab_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="实验室审批通过"
        )
        await approval_service.perform_approval(quotation.id, lab_action, lab_current_user)

        # 5. 现场审批通过
        field_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=field_user.user_id, user_name=field_user.user_name)
        )
        field_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="现场审批通过"
        )
        await approval_service.perform_approval(quotation.id, field_action, field_current_user)

        # 6. 验证最终状态
        final_status = await approval_service.get_approval_status(quotation.id)
        assert final_status.overall_status == "2"  # 已审核
        
        # 验证所有审批记录都已通过
        for record in final_status.approval_records:
            assert record.approval_status == "approved"

    @pytest.mark.asyncio
    async def test_complete_sample_approval_workflow(self, setup_integration_test_data):
        """
        测试完整的送样审批流程
        """
        test_data = await setup_integration_test_data
        quotation = test_data["quotation_sample"]
        market_user = test_data["market_user"]
        lab_user = test_data["lab_user"]
        db = quotation.__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 1. 初始化审批记录
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sample", current_user)

        # 验证送样项目的审批记录
        status = await approval_service.get_approval_status(quotation.id)
        field_records = [r for r in status.approval_records if r.approver_type == "field"]
        # 送样项目不应该有现场审批记录
        assert len(field_records) == 0
        
        # 验证只有市场和实验室审批记录
        assert len(status.approval_records) == 2
        approver_types = [r.approver_type for r in status.approval_records]
        assert "market" in approver_types
        assert "lab" in approver_types

        # 2. 提交审批
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 3. 市场审批通过
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="市场审批通过"
        )
        await approval_service.perform_approval(quotation.id, market_action, current_user)

        # 4. 实验室审批通过
        lab_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=lab_user.user_id, user_name=lab_user.user_name)
        )
        lab_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="实验室审批通过"
        )
        await approval_service.perform_approval(quotation.id, lab_action, lab_current_user)

        # 5. 验证最终状态（送样项目不需要现场审批）
        final_status = await approval_service.get_approval_status(quotation.id)
        assert final_status.overall_status == "2"  # 已审核

    @pytest.mark.asyncio
    async def test_approval_rejection_workflow(self, setup_integration_test_data):
        """
        测试审批拒绝流程
        """
        test_data = await setup_integration_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        db = quotation.__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 1. 初始化并提交审批
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 2. 市场审批拒绝
        rejection_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="rejected",
            approval_opinion="项目信息不完整，需要补充"
        )
        await approval_service.perform_approval(quotation.id, rejection_action, current_user)

        # 3. 验证拒绝后的状态
        final_status = await approval_service.get_approval_status(quotation.id)
        assert final_status.overall_status == "4"  # 已拒绝

        # 验证拒绝记录
        market_record = next(r for r in final_status.approval_records if r.approver_type == "market")
        assert market_record.approval_status == "rejected"
        assert market_record.approval_opinion == "项目信息不完整，需要补充"

    @pytest.mark.asyncio
    async def test_pending_approvals_workflow(self, setup_integration_test_data):
        """
        测试待审批列表功能
        """
        test_data = await setup_integration_test_data
        quotation = test_data["quotation_sampling"]
        market_user = test_data["market_user"]
        lab_user = test_data["lab_user"]
        db = quotation.__dict__['_sa_instance_state'].session

        approval_service = ProjectQuotationApprovalService(db)

        # 1. 初始化并提交审批
        current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=market_user.user_id, user_name=market_user.user_name)
        )
        await approval_service.init_approval_records(quotation.id, "sampling", current_user)
        await approval_service.submit_for_approval(quotation.id, current_user)

        # 2. 检查市场用户的待审批列表
        market_pending = await approval_service.get_pending_approvals(current_user)
        assert len(market_pending) > 0
        assert any(item["id"] == quotation.id for item in market_pending)

        # 3. 检查实验室用户的待审批列表（应该为空，因为市场审批未完成）
        lab_current_user = CurrentUserModel(
            permissions=[],
            roles=[],
            user=UserInfoModel(user_id=lab_user.user_id, user_name=lab_user.user_name)
        )
        lab_pending = await approval_service.get_pending_approvals(lab_current_user)
        # 实验室用户暂时没有可审批的项目（市场审批未完成）
        assert len(lab_pending) == 0

        # 4. 市场审批通过后，检查实验室用户的待审批列表
        market_action = ApprovalActionModel(
            project_quotation_id=quotation.id,
            approval_status="approved",
            approval_opinion="市场审批通过"
        )
        await approval_service.perform_approval(quotation.id, market_action, current_user)

        # 现在实验室用户应该有待审批的项目
        lab_pending_after = await approval_service.get_pending_approvals(lab_current_user)
        assert len(lab_pending_after) > 0
        assert any(item["id"] == quotation.id for item in lab_pending_after)

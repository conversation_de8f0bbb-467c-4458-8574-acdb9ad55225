<template>
  <el-dialog
    title="审批流程"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    @close="handleClose"
  >
    <div v-if="quotationData">
      <!-- 项目基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span>项目基本信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目编号：</span>
              <span>{{ quotationData.projectCode }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目名称：</span>
              <span>{{ quotationData.projectName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">业务类别：</span>
              <el-tag :type="quotationData.businessType === 'sampling' ? 'primary' : 'success'">
                {{ quotationData.businessType === 'sampling' ? '一般采样' : '送样' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">当前状态：</span>
              <el-tag :type="getStatusTagType(currentDetailedStatus || quotationData.status)" size="small">
                {{ currentDetailedStatusLabel || getStatusLabel(quotationData.status) }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">客户名称：</span>
              <span>{{ quotationData.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">合同编号：</span>
              <span>{{ quotationData.contractCode || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审批流程图 -->
      <el-card class="mb-4">
        <template #header>
          <span>审批流程</span>
        </template>
        <div class="approval-flow">
          <div class="flow-step" :class="{ 'completed': isStageCompleted(1), 'current': isCurrentStage(1) }">
            <div class="step-icon">
              <el-icon v-if="isStageCompleted(1)"><Check /></el-icon>
              <span v-else>1</span>
            </div>
            <div class="step-title">市场审批</div>
            <div class="step-status">{{ getStageStatusText(1) }}</div>
          </div>

          <div class="flow-arrow">→</div>

          <div class="flow-step" :class="{ 'completed': isStageCompleted(2), 'current': isCurrentStage(2) }">
            <div class="step-icon">
              <el-icon v-if="isStageCompleted(2)"><Check /></el-icon>
              <span v-else>2</span>
            </div>
            <div class="step-title">
              <div v-if="quotationData.businessType === 'sampling'">实验室 + 现场审批</div>
              <div v-else>实验室审批</div>
            </div>
            <div class="step-status">{{ getStageStatusText(2) }}</div>
          </div>

          <div class="flow-arrow">→</div>

          <div class="flow-step" :class="{ 'completed': quotationData.status === '2' }">
            <div class="step-icon">
              <el-icon v-if="quotationData.status === '2'"><Check /></el-icon>
              <span v-else>3</span>
            </div>
            <div class="step-title">审批完成</div>
            <div class="step-status">{{ quotationData.status === '2' ? '已完成' : '待完成' }}</div>
          </div>
        </div>
      </el-card>

      <!-- 审批历史记录 -->
      <el-card>
        <template #header>
          <span>审批流程与历史</span>
        </template>
        <div v-if="approvalRecords.length === 0" class="no-records">
          <el-empty description="暂无审批记录" />
        </div>
        <el-timeline v-else>
          <el-timeline-item
            v-for="record in approvalRecords"
            :key="record.approverType"
            :timestamp="record.approvalTime || '待审批'"
            :type="getTimelineType(record.approvalStatus)"
          >
            <div>
              <div class="approval-record-header">
                <span class="approver-role">{{ getApproverTypeLabel(record.approverType) }}</span>
                <el-tag :type="getRecordStatusTagType(record.approvalStatus)" size="small">
                  {{ getRecordStatusLabel(record.approvalStatus) }}
                </el-tag>
                <span v-if="record.isRequired === '0'" class="optional-tag">(可选)</span>
              </div>

              <!-- 显示审批人列表 -->
              <div class="approver-list">
                <div class="approver-list-label">可审批人员：</div>
                <div class="approver-names">
                  <span v-if="record.approverName && !record.approverName.includes('待分配')" class="actual-approver">
                    实际审批人：{{ record.approverName }}
                  </span>
                  <span v-else class="available-approvers">
                    {{ record.approverName || '暂无可用审批人' }}
                  </span>
                </div>
              </div>

              <div v-if="record.approvalOpinion" class="approval-comment">
                <div class="comment-label">审批意见：</div>
                <div class="comment-content">{{ record.approvalOpinion }}</div>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { getProjectQuotation } from '@/api/quotation/projectQuotation'
import { getApprovalStatus } from '@/api/quotation/projectQuotationApproval'
import { Check } from '@element-plus/icons-vue'

export default {
  name: 'ApprovalFlowDialog',
  components: {
    Check
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    quotationId: {
      type: [Number, String],
      default: null
    }
  },
  emits: ['update:visible'],
  data() {
    return {
      dialogVisible: false,
      loading: false,
      quotationData: null,
      approvalRecords: [],
      currentDetailedStatus: null,
      currentDetailedStatusLabel: null
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val && this.quotationId) {
          this.loadData()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 加载数据 */
    async loadData() {
      this.loading = true
      try {
        // 加载项目基本信息
        await this.loadQuotationData()
        // 加载审批状态和历史记录
        await this.loadApprovalStatus()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$modal.msgError('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    /** 加载项目报价数据 */
    async loadQuotationData() {
      const response = await getProjectQuotation(this.quotationId)
      if (response.code === 200) {
        this.quotationData = response.data
      }
    },

    /** 加载审批状态和历史记录 */
    async loadApprovalStatus() {
      try {
        const response = await getApprovalStatus(this.quotationId)
        if (response.code === 200) {
          this.approvalRecords = response.data.approvalRecords || []
          // 计算当前详细状态
          this.calculateCurrentStatus(response.data)
        }
      } catch (error) {
        // 如果项目还没有审批记录，这是正常的
        console.log('审批状态加载失败，可能项目还未提交审批:', error)
        this.approvalRecords = []
      }
    },

    /** 计算当前状态 */
    calculateCurrentStatus(statusData) {
      if (!statusData || !this.quotationData) {
        this.currentDetailedStatus = null
        this.currentDetailedStatusLabel = null
        return
      }

      const status = this.quotationData.status
      const businessType = this.quotationData.businessType
      const records = statusData.approvalRecords || []

      // 如果项目状态不是待审核，直接使用项目状态
      if (status !== '1') {
        this.currentDetailedStatus = status
        this.currentDetailedStatusLabel = this.getStatusLabel(status)
        return
      }

      // 计算详细状态
      const stage1Records = records.filter(r => r.approvalStage === 1 && r.isRequired === '1')
      const stage2Records = records.filter(r => r.approvalStage === 2 && r.isRequired === '1')

      const stage1Completed = stage1Records.length > 0 && stage1Records.every(r => r.approvalStatus === 'approved')
      const stage2Completed = stage2Records.length > 0 && stage2Records.every(r => r.approvalStatus === 'approved')

      if (!stage1Completed) {
        this.currentDetailedStatus = '1-market'
        this.currentDetailedStatusLabel = '待审核（市场）'
      } else if (!stage2Completed) {
        if (businessType === 'sampling') {
          this.currentDetailedStatus = '1-lab|field'
          this.currentDetailedStatusLabel = '待审核（实验室|现场）'
        } else {
          this.currentDetailedStatus = '1-lab'
          this.currentDetailedStatusLabel = '待审核（实验室）'
        }
      } else {
        this.currentDetailedStatus = '2'
        this.currentDetailedStatusLabel = '审核完成'
      }
    },

    /** 判断阶段是否完成 */
    isStageCompleted(stage) {
      if (!this.approvalRecords.length) return false

      const stageRecords = this.approvalRecords.filter(r => r.approvalStage === stage && r.isRequired === '1')
      return stageRecords.length > 0 && stageRecords.every(r => r.approvalStatus === 'approved')
    },

    /** 判断是否是当前阶段 */
    isCurrentStage(stage) {
      if (!this.quotationData || this.quotationData.status !== '1') return false

      if (stage === 1) {
        return !this.isStageCompleted(1)
      } else if (stage === 2) {
        return this.isStageCompleted(1) && !this.isStageCompleted(2)
      }
      return false
    },

    /** 获取阶段状态文本 */
    getStageStatusText(stage) {
      if (this.isStageCompleted(stage)) {
        return '已完成'
      } else if (this.isCurrentStage(stage)) {
        return '进行中'
      } else {
        return '待开始'
      }
    },

    /** 获取审批人类型标签 */
    getApproverTypeLabel(type) {
      const typeMap = {
        'market': '市场审批',
        'lab': '实验室审批',
        'field': '现场审批'
      }
      return typeMap[type] || type
    },

    /** 获取时间线类型 */
    getTimelineType(status) {
      return status === 'approved' ? 'success' : status === 'rejected' ? 'danger' : 'primary'
    },

    /** 获取记录状态标签类型 */
    getRecordStatusTagType(status) {
      return status === 'approved' ? 'success' : status === 'rejected' ? 'danger' : 'warning'
    },

    /** 获取记录状态标签 */
    getRecordStatusLabel(status) {
      const statusMap = {
        'pending': '待审批',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return statusMap[status] || status
    },

    /** 获取状态标签 */
    getStatusLabel(status) {
      const statusMap = {
        '0': '草稿',
        '1': '待审核',
        '2': '审核完成',
        '3': '已撤回',
        '4': '已拒绝'
      }
      return statusMap[status] || '未知'
    },

    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusStr = String(status)
      if (statusStr === '0') return 'info'           // 草稿 - 灰色
      if (statusStr === '2') return 'success'        // 审核完成 - 绿色
      if (statusStr === '3') return 'warning'        // 已撤回 - 橙色
      if (statusStr === '4') return 'danger'         // 已拒绝 - 红色
      if (statusStr === '1-market') return 'primary' // 市场审批 - 蓝色
      if (statusStr === '1-lab') return 'primary'    // 实验室审批 - 蓝色
      if (statusStr === '1-field') return 'primary'  // 现场审批 - 蓝色
      if (statusStr === '1-lab|field') return 'primary' // 实验室|现场审批 - 蓝色
      return 'primary'                            // 默认待审核 - 蓝色
    },

    /** 关闭弹框 */
    handleClose() {
      this.dialogVisible = false
      this.quotationData = null
      this.approvalRecords = []
      this.currentDetailedStatus = null
      this.currentDetailedStatusLabel = null
    }
  }
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.approval-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.flow-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  border: 2px solid #dcdfe6;
  color: #909399;
  font-weight: bold;
  margin-bottom: 8px;
}

.flow-step.completed .step-icon {
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
}

.flow-step.current .step-icon {
  background-color: #409eff;
  border-color: #409eff;
  color: white;
}

.step-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  text-align: center;
}

.step-status {
  font-size: 12px;
  color: #909399;
}

.flow-step.completed .step-status {
  color: #67c23a;
}

.flow-step.current .step-status {
  color: #409eff;
}

.flow-arrow {
  font-size: 20px;
  color: #dcdfe6;
  margin: 0 20px;
}

.approval-record-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.approver-role {
  font-weight: 500;
  color: #303133;
}

.approver-name {
  color: #606266;
}

.optional-tag {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.approver-list {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.approver-list-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.approver-names {
  color: #303133;
  line-height: 1.5;
}

.actual-approver {
  font-weight: 500;
  color: #67c23a;
}

.available-approvers {
  color: #606266;
}

.approval-comment {
  background-color: #f8f9fa;
  border-left: 3px solid #409eff;
  padding: 8px 12px;
  margin-top: 8px;
  border-radius: 4px;
}

.comment-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.comment-content {
  color: #303133;
  line-height: 1.5;
}

.no-records {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
